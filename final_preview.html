<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分摊表管理 - 最终效果预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 引入分摊表管理页面样式 */
        .container-div {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .table-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            margin-bottom: 0;
            box-shadow: 0 2px 10px rgba(116, 185, 255, 0.2);
            border-bottom: 3px solid #1976d2;
        }
        
        .table-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .table-header h2 i {
            margin-right: 12px;
            font-size: 22px;
        }
        
        .search-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .search-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        
        .search-form ul {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .search-form li {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .search-form li:last-child {
            grid-column: 1 / -1;
            display: flex;
            flex-direction: row;
            justify-content: center;
            gap: 15px;
            margin-top: 10px;
        }
        
        .search-form label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }
        
        .search-form input,
        .search-form select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .action-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: flex-start;
            align-items: center;
        }
        
        .action-btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            border: none;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }
        
        .confirm-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        }
        
        .export-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
        }
        
        .export-selected-btn {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(162, 155, 254, 0.3);
        }
        
        .notify-btn {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
        }
        
        .search-toggle-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 12px rgba(116, 185, 255, 0.3);
            margin-left: auto;
        }
        
        .search-info-row {
            margin-top: 10px;
            text-align: center;
        }
        
        .search-info {
            color: #6c757d;
            font-size: 13px;
            font-style: italic;
        }
        
        .search-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        }
        
        .reset-btn {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
        }
        
        .table-section {
            background: white;
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 2px solid #e3f2fd;
            overflow: hidden;
        }
        
        .table {
            border: none;
            border-radius: 0;
            overflow: hidden;
            box-shadow: none;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            font-weight: 700;
            border: 2px solid #e3f2fd;
            border-bottom: 3px solid #1976d2;
            padding: 18px 15px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e3f2fd;
        }
        
        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.15);
        }
        
        .table tbody td {
            padding: 15px 12px;
            vertical-align: middle;
            border-left: 1px solid #e3f2fd;
            border-right: 1px solid #e3f2fd;
        }
        
        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }
        
        .badge-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }
        
        .badge-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 15px;
            margin: 2px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <form id="formId">
                        <!-- 默认隐藏的筛选表单 -->
                        <div class="search-form" id="searchForm" style="display: none;">
                            <ul>
                                <li>
                                    <label>分摊表名称：</label>
                                    <input type="text" name="tableName" placeholder="请输入分摊表名称"/>
                                </li>
                                <li>
                                    <label>计费周期：</label>
                                    <input type="text" name="billingCycle" placeholder="请输入计费周期"/>
                                </li>
                                <li>
                                    <label>费用类型：</label>
                                    <select name="expenseType" id="expenseType">
                                        <option value="">请选择费用类型</option>
                                        <option value="电费">电费</option>
                                        <option value="水费">水费</option>
                                        <option value="物业费">物业费</option>
                                    </select>
                                </li>
                                <li>
                                    <label>状态：</label>
                                    <select name="status" id="status">
                                        <option value="">请选择状态</option>
                                        <option value="审核中">审核中</option>
                                        <option value="已审核">已审核</option>
                                        <option value="已拒绝">已拒绝</option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn search-btn"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn reset-btn"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 操作按钮区域 -->
            <div class="col-sm-12">
                <div class="action-section">
                    <div class="action-buttons">
                        <a class="btn action-btn confirm-btn">
                            <i class="fa fa-check-circle"></i> 一键确认
                        </a>
                        <a class="btn action-btn export-btn">
                            <i class="fa fa-download"></i> 一键导出
                        </a>
                        <a class="btn action-btn export-selected-btn">
                            <i class="fa fa-download"></i> 导出选中
                        </a>
                        <a class="btn action-btn notify-btn">
                            <i class="fa fa-bell"></i> 通知核对
                        </a>
                        <a class="btn action-btn delete-btn">
                            <i class="fa fa-trash"></i> 删除
                        </a>
                        <!-- 筛选按钮 - 位于最右侧 -->
                        <a class="btn search-toggle-btn" onclick="toggleSearch()">
                            <i class="fa fa-filter" style="margin-right: 8px;"></i>
                            <i class="fa fa-chevron-down"></i> 筛选
                        </a>
                    </div>
                </div>
            </div>

            <!-- 表格区域 -->
            <div class="col-sm-12">
                <div class="table-section">
                    <!-- 表格标题 -->
                    <div class="table-header">
                        <h2><i class="fa fa-table"></i>分摊表管理</h2>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th><input type="checkbox"></th>
                                <th>分摊表名称</th>
                                <th>计费周期</th>
                                <th>费用类型</th>
                                <th>制表人</th>
                                <th>复核人</th>
                                <th>负责人</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>2025年1月电费分摊表</td>
                                <td>202501</td>
                                <td>电费</td>
                                <td>张三</td>
                                <td>李四</td>
                                <td>王五</td>
                                <td><span class="badge badge-warning">审核中</span></td>
                                <td>2025-01-15 10:30:00</td>
                                <td>
                                    <a class="btn btn-success btn-xs"><i class="fa fa-check"></i> 确认</a>
                                    <a class="btn btn-danger btn-xs"><i class="fa fa-times"></i> 退回</a>
                                    <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>2024年12月水费分摊表</td>
                                <td>202412</td>
                                <td>水费</td>
                                <td>赵六</td>
                                <td>钱七</td>
                                <td>孙八</td>
                                <td><span class="badge badge-success">已审核</span></td>
                                <td>2024-12-28 14:20:00</td>
                                <td>
                                    <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                                    <a class="btn btn-primary btn-xs"><i class="fa fa-download"></i> 导出</a>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>2024年11月物业费分摊表</td>
                                <td>202411</td>
                                <td>物业费</td>
                                <td>周九</td>
                                <td>吴十</td>
                                <td>郑十一</td>
                                <td><span class="badge badge-danger">已拒绝</span></td>
                                <td>2024-11-25 16:45:00</td>
                                <td>
                                    <a class="btn btn-warning btn-xs"><i class="fa fa-edit"></i> 修改</a>
                                    <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>2024年10月网络费分摊表</td>
                                <td>202410</td>
                                <td>网络费</td>
                                <td>王十二</td>
                                <td>李十三</td>
                                <td>张十四</td>
                                <td><span class="badge badge-success">已审核</span></td>
                                <td>2024-10-30 09:15:00</td>
                                <td>
                                    <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                                    <a class="btn btn-primary btn-xs"><i class="fa fa-download"></i> 导出</a>
                                </td>
                            </tr>
                            <tr>
                                <td><input type="checkbox"></td>
                                <td>2024年9月清洁费分摊表</td>
                                <td>202409</td>
                                <td>清洁费</td>
                                <td>赵十五</td>
                                <td>钱十六</td>
                                <td>孙十七</td>
                                <td><span class="badge badge-warning">审核中</span></td>
                                <td>2024-09-28 13:20:00</td>
                                <td>
                                    <a class="btn btn-success btn-xs"><i class="fa fa-check"></i> 确认</a>
                                    <a class="btn btn-danger btn-xs"><i class="fa fa-times"></i> 退回</a>
                                    <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSearch() {
            const form = document.getElementById('searchForm');
            const btn = document.querySelector('.search-toggle-btn');
            
            if (form.style.display === 'none') {
                form.style.display = 'block';
                btn.innerHTML = '<i class="fa fa-filter" style="margin-right: 8px;"></i><i class="fa fa-chevron-up"></i> 收起筛选';
            } else {
                form.style.display = 'none';
                btn.innerHTML = '<i class="fa fa-filter" style="margin-right: 8px;"></i><i class="fa fa-chevron-down"></i> 筛选';
            }
        }
    </script>
</body>
</html>
