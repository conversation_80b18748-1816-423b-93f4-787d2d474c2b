<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分摊表管理 - 美化预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 页面整体样式优化 */
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .container-div {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        /* 页面标题样式 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .page-header h1 i {
            margin-right: 12px;
            font-size: 24px;
        }
        
        /* 搜索区域样式优化 */
        .search-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .search-toggle-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
        }
        
        .search-toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
            color: white;
        }
        
        .search-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
            margin-top: 15px;
        }
        
        .search-form .form-row {
            margin-bottom: 15px;
        }
        
        .search-form label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .search-form input,
        .search-form select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .search-form input:focus,
        .search-form select:focus {
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
            outline: none;
        }
        
        /* 操作按钮区域样式 */
        .action-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: flex-start;
            align-items: center;
        }
        
        .action-btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            border: none;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            text-decoration: none;
        }
        
        .confirm-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        }
        
        .confirm-btn:hover {
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
            color: white;
        }
        
        .export-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
        }
        
        .export-btn:hover {
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
            color: white;
        }
        
        .export-selected-btn {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(162, 155, 254, 0.3);
        }
        
        .export-selected-btn:hover {
            box-shadow: 0 4px 15px rgba(162, 155, 254, 0.4);
            color: white;
        }
        
        .notify-btn {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
        }
        
        .notify-btn:hover {
            box-shadow: 0 4px 15px rgba(253, 203, 110, 0.4);
            color: white;
        }
        
        .delete-btn {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
        }
        
        .delete-btn:hover {
            box-shadow: 0 4px 15px rgba(253, 121, 168, 0.4);
            color: white;
        }
        
        /* 表格区域样式 */
        .table-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 15px 12px;
            font-size: 14px;
        }
        
        .table tbody tr {
            transition: all 0.2s ease;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
        }
        
        .table tbody td {
            padding: 12px;
            vertical-align: middle;
            border-color: #e9ecef;
        }
        
        /* 状态标签样式优化 */
        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }
        
        .badge-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }
        
        .badge-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 15px;
            margin: 2px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-xs:hover {
            transform: translateY(-1px);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
            color: white;
        }
        
        .reset-btn {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
        }
        
        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(253, 203, 110, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-div">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1><i class="fa fa-table"></i>分摊表管理</h1>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <div>
                <a class="btn search-toggle-btn" onclick="toggleSearch()">
                    <i class="fa fa-chevron-down"></i> 展开筛选条件
                </a>
            </div>
            <div class="search-form" id="searchForm" style="display: none;">
                <div class="row">
                    <div class="col-md-3">
                        <label>分摊表名称：</label>
                        <input type="text" class="form-control" placeholder="请输入分摊表名称"/>
                    </div>
                    <div class="col-md-3">
                        <label>计费周期：</label>
                        <input type="text" class="form-control" placeholder="请输入计费周期"/>
                    </div>
                    <div class="col-md-3">
                        <label>费用类型：</label>
                        <select class="form-control">
                            <option value="">请选择费用类型</option>
                            <option value="电费">电费</option>
                            <option value="水费">水费</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label>状态：</label>
                        <select class="form-control">
                            <option value="">请选择状态</option>
                            <option value="审核中">审核中</option>
                            <option value="已审核">已审核</option>
                            <option value="已拒绝">已拒绝</option>
                        </select>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12 text-center">
                        <a class="btn search-btn"><i class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn reset-btn"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮区域 -->
        <div class="action-section">
            <div class="action-buttons">
                <a class="btn action-btn confirm-btn">
                    <i class="fa fa-check-circle"></i> 一键确认
                </a>
                <a class="btn action-btn export-btn">
                    <i class="fa fa-download"></i> 一键导出
                </a>
                <a class="btn action-btn export-selected-btn">
                    <i class="fa fa-download"></i> 导出选中
                </a>
                <a class="btn action-btn notify-btn">
                    <i class="fa fa-bell"></i> 通知核对
                </a>
                <a class="btn action-btn delete-btn">
                    <i class="fa fa-trash"></i> 删除
                </a>
            </div>
        </div>
        
        <!-- 表格区域 -->
        <div class="table-section">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th><input type="checkbox"></th>
                        <th>分摊表名称</th>
                        <th>计费周期</th>
                        <th>费用类型</th>
                        <th>制表人</th>
                        <th>复核人</th>
                        <th>负责人</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2025年1月电费分摊表</td>
                        <td>202501</td>
                        <td>电费</td>
                        <td>张三</td>
                        <td>李四</td>
                        <td>王五</td>
                        <td><span class="badge badge-warning">审核中</span></td>
                        <td>2025-01-15 10:30:00</td>
                        <td>
                            <a class="btn btn-success btn-xs"><i class="fa fa-check"></i> 确认</a>
                            <a class="btn btn-danger btn-xs"><i class="fa fa-times"></i> 退回</a>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2024年12月水费分摊表</td>
                        <td>202412</td>
                        <td>水费</td>
                        <td>赵六</td>
                        <td>钱七</td>
                        <td>孙八</td>
                        <td><span class="badge badge-success">已审核</span></td>
                        <td>2024-12-28 14:20:00</td>
                        <td>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                            <a class="btn btn-primary btn-xs"><i class="fa fa-download"></i> 导出</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function toggleSearch() {
            const form = document.getElementById('searchForm');
            const btn = document.querySelector('.search-toggle-btn');
            
            if (form.style.display === 'none') {
                form.style.display = 'block';
                btn.innerHTML = '<i class="fa fa-chevron-up"></i> 收起筛选条件';
            } else {
                form.style.display = 'none';
                btn.innerHTML = '<i class="fa fa-chevron-down"></i> 展开筛选条件';
            }
        }
    </script>
</body>
</html>
