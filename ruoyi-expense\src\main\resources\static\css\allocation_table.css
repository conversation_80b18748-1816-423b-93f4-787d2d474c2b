/* 分摊表管理页面样式 */

/* 页面整体样式优化 */
.container-div {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

/* 表格标题样式 */
.table-header {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 12px 12px 0 0;
    margin-bottom: 0;
    box-shadow: 0 2px 10px rgba(116, 185, 255, 0.2);
    border-bottom: 3px solid #1976d2;
}

.table-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.table-header h2 i {
    margin-right: 12px;
    font-size: 22px;
}

/* 搜索区域样式优化 */
.search-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.search-info-row {
    margin-top: 10px;
    text-align: center;
}

.search-toggle-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 12px rgba(116, 185, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.search-toggle-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.search-toggle-btn:hover:before {
    left: 100%;
}

.search-toggle-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
    color: white;
}

.search-info {
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
}

.search-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.search-form ul {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.search-form li {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-form li:last-child {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
}

.search-form label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.search-form input,
.search-form select {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.search-form input:focus,
.search-form select:focus {
    border-color: #74b9ff;
    box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
    outline: none;
}

.search-btn {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
    color: white;
}

.reset-btn {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    border: none;
    color: white;
    padding: 10px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(253, 203, 110, 0.4);
    color: white;
}

/* 操作按钮区域样式 */
.action-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: flex-start;
    align-items: center;
}

.action-btn {
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    border: none;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.action-btn:hover {
    transform: translateY(-2px);
    text-decoration: none;
}

.confirm-btn {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
}

.confirm-btn:hover {
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
    color: white;
}

.export-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.export-btn:hover {
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
    color: white;
}

.export-selected-btn {
    background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(162, 155, 254, 0.3);
}

.export-selected-btn:hover {
    box-shadow: 0 4px 15px rgba(162, 155, 254, 0.4);
    color: white;
}

.notify-btn {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
}

.notify-btn:hover {
    box-shadow: 0 4px 15px rgba(253, 203, 110, 0.4);
    color: white;
}

.delete-btn {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
}

.delete-btn:hover {
    box-shadow: 0 4px 15px rgba(253, 121, 168, 0.4);
    color: white;
}

/* 表格区域样式 */
.table-section {
    background: white;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border: 2px solid #e3f2fd;
    overflow: hidden;
}

/* Bootstrap Table 样式优化 */
.fixed-table-container {
    border-radius: 0 0 12px 12px;
    overflow: hidden;
    border: none;
    box-shadow: none;
}

.fixed-table-container .table {
    margin-bottom: 0;
    border: none;
    border-radius: 0;
}

.table thead th {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    font-weight: 700;
    border: 2px solid #e3f2fd;
    border-bottom: 3px solid #1976d2;
    padding: 18px 15px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.table thead th:first-child {
    border-radius: 0;
    border-left: 3px solid #1976d2;
}

.table thead th:last-child {
    border-radius: 0;
    border-right: 3px solid #1976d2;
}

.table thead th:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #00b894, #74b9ff, #0984e3);
    opacity: 0.8;
}

.table {
    border: none;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
}

.table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e3f2fd;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.15);
}

.table tbody tr:last-child {
    border-bottom: none;
}

.table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-left: 1px solid #e3f2fd;
    border-right: 1px solid #e3f2fd;
    position: relative;
}

.table tbody td:first-child {
    border-left: 2px solid #74b9ff;
}

.table tbody td:last-child {
    border-right: 2px solid #74b9ff;
}

.table tbody tr:nth-child(even) {
    background-color: #fafbfc;
}

.table tbody tr:nth-child(odd) {
    background-color: white;
}

/* 状态标签样式优化 */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.badge-warning {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
}

.badge-success {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
}

.badge-danger {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
}

/* 操作按钮样式优化 */
.table .btn-xs {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 15px;
    margin: 2px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.table .btn-xs:hover {
    transform: translateY(-1px);
}

/* 模态框样式优化 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 20px 25px;
    border: none;
}

.modal-title {
    font-weight: 600;
    font-size: 18px;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border: none;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #74b9ff;
    box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
}

.text-danger {
    color: #e84393 !important;
}

.form-text {
    color: #6c757d;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-div {
        padding: 15px;
    }

    .table-header {
        padding: 15px;
    }

    .table-header h2 {
        font-size: 20px;
    }

    .search-form ul {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        justify-content: center;
    }

    .action-btn {
        min-width: 100px;
        padding: 10px 16px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }

    .action-btn {
        width: 100%;
        min-width: auto;
    }
}
