<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分摊表管理')" />
    <style>
        /* 页面整体样式优化 */
        .container-div {
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }

        /* 表格标题样式 */
        .table-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            margin-bottom: 0;
            box-shadow: 0 2px 10px rgba(116, 185, 255, 0.2);
            border-bottom: 3px solid #1976d2;
        }

        .table-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .table-header h2 i {
            margin-right: 12px;
            font-size: 18px;
        }

        /* 搜索区域样式优化 */
        .search-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .search-collapse-header {
            margin-bottom: 15px !important;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-toggle-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 30px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 12px rgba(116, 185, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .search-toggle-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .search-toggle-btn:hover:before {
            left: 100%;
        }

        .search-toggle-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
            color: white;
        }

        .search-info {
            color: #6c757d;
            font-size: 13px;
            font-style: italic;
        }

        .search-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .search-form ul {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .search-form li {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .search-form li:last-child {
            grid-column: 1 / -1;
            display: flex;
            flex-direction: row;
            justify-content: center;
            gap: 15px;
            margin-top: 10px;
        }

        .search-form label {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .search-form input,
        .search-form select {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .search-form input:focus,
        .search-form select:focus {
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
            outline: none;
        }

        .search-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
            color: white;
        }

        .reset-btn {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(253, 203, 110, 0.4);
            color: white;
        }

        /* 操作按钮区域样式 */
        .action-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: flex-start;
            align-items: center;
        }

        .action-btn {
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            border: none;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            text-decoration: none;
        }

        .confirm-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(0, 184, 148, 0.3);
        }

        .confirm-btn:hover {
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
            color: white;
        }

        .export-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
        }

        .export-btn:hover {
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.4);
            color: white;
        }

        .export-selected-btn {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(162, 155, 254, 0.3);
        }

        .export-selected-btn:hover {
            box-shadow: 0 4px 15px rgba(162, 155, 254, 0.4);
            color: white;
        }

        .notify-btn {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(253, 203, 110, 0.3);
        }

        .notify-btn:hover {
            box-shadow: 0 4px 15px rgba(253, 203, 110, 0.4);
            color: white;
        }

        .delete-btn {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(253, 121, 168, 0.3);
        }

        .delete-btn:hover {
            box-shadow: 0 4px 15px rgba(253, 121, 168, 0.4);
            color: white;
        }

        /* 表格区域样式 */
        .table-section {
            background: white;
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 2px solid #e3f2fd;
            overflow: hidden;
        }

        /* Bootstrap Table 样式优化 */
        .fixed-table-container {
            border-radius: 0 0 12px 12px;
            overflow: hidden;
            border: none;
            box-shadow: none;
        }

        .fixed-table-container .table {
            margin-bottom: 0;
            border: none;
            border-radius: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            font-weight: 700;
            border: 2px solid #e3f2fd;
            border-bottom: 3px solid #1976d2;
            padding: 18px 15px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .table thead th:first-child {
            border-radius: 0;
            border-left: 3px solid #1976d2;
        }

        .table thead th:last-child {
            border-radius: 0;
            border-right: 3px solid #1976d2;
        }

        .table thead th:before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #00b894, #74b9ff, #0984e3);
            opacity: 0.8;
        }

        .table {
            border: 2px solid #e3f2fd;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.1);
        }

        .table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e3f2fd;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.15);
        }

        .table tbody tr:last-child {
            border-bottom: none;
        }

        .table tbody td {
            padding: 15px 12px;
            vertical-align: middle;
            border-left: 1px solid #e3f2fd;
            border-right: 1px solid #e3f2fd;
            position: relative;
        }

        .table tbody td:first-child {
            border-left: 2px solid #74b9ff;
        }

        .table tbody td:last-child {
            border-right: 2px solid #74b9ff;
        }

        .table tbody tr:nth-child(even) {
            background-color: #fafbfc;
        }

        .table tbody tr:nth-child(odd) {
            background-color: white;
        }

        /* 状态标签样式优化 */
        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .badge-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }

        .badge-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }

        /* 操作按钮样式优化 */
        .table .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 15px;
            margin: 2px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .table .btn-xs:hover {
            transform: translateY(-1px);
        }

        /* 模态框样式优化 */
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px 25px;
            border: none;
        }

        .modal-title {
            font-weight: 600;
            font-size: 18px;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-footer {
            padding: 20px 25px;
            border: none;
            background: #f8f9fa;
            border-radius: 0 0 15px 15px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
        }

        .text-danger {
            color: #e84393 !important;
        }

        .form-text {
            color: #6c757d;
            font-size: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container-div {
                padding: 15px;
            }

            .page-header {
                padding: 20px;
                margin-bottom: 20px;
            }

            .page-header h1 {
                font-size: 24px;
            }

            .search-form ul {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                justify-content: center;
            }

            .action-btn {
                min-width: 100px;
                padding: 10px 16px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .action-buttons {
                flex-direction: column;
                width: 100%;
            }

            .action-btn {
                width: 100%;
                min-width: auto;
            }
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">

        <div class="row">
            <div class="col-sm-12">
                <!-- 搜索区域 -->
                <div class="search-section">
                    <form id="formId">
                        <!-- 添加折叠控制按钮 -->
                        <div class="search-collapse-header">
                            <a class="btn search-toggle-btn" onclick="toggleSearchForm()" id="searchToggleBtn">
                                <i class="fa fa-filter" style="margin-right: 8px;"></i>
                                <i class="fa fa-chevron-down" id="searchToggleIcon"></i> 高级筛选
                            </a>
                            <span class="search-info">点击展开更多筛选选项</span>
                        </div>
                        <!-- 默认隐藏的筛选表单 -->
                        <div class="search-form" id="searchForm" style="display: none;">
                            <ul>
                                <li>
                                    <label>分摊表名称：</label>
                                    <input type="text" name="tableName" placeholder="请输入分摊表名称"/>
                                </li>
                                <li>
                                    <label>计费周期：</label>
                                    <input type="text" name="billingCycle" placeholder="请输入计费周期"/>
                                </li>
                                <li>
                                    <label>费用类型：</label>
                                    <select name="expenseType" id="expenseType">
                                        <option value="">请选择费用类型</option>
                                    </select>
                                </li>
                                <li>
                                    <label>状态：</label>
                                    <select name="status" id="status">
                                        <option value="">请选择状态</option>
                                        <option value="审核中">审核中</option>
                                        <option value="已审核">已审核</option>
                                        <option value="已拒绝">已拒绝</option>
                                    </select>
                                </li>
                                <li>
                                    <a class="btn search-btn" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn reset-btn" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="col-sm-12">
                <div class="action-section">
                    <div class="action-buttons">
                        <!-- 按照要求的顺序：一键确认、一键导出、导出选中、通知核对 -->
                        <a class="btn action-btn confirm-btn" onclick="confirmAllPending()" shiro:hasAnyRoles="admin,expense_allocationTable_checker">
                            <i class="fa fa-check-circle"></i> 一键确认
                        </a>
                        <a class="btn action-btn export-btn" onclick="exportAllPendingAllocationTables()">
                            <i class="fa fa-download"></i> 一键导出
                        </a>
                        <a class="btn action-btn export-selected-btn" onclick="exportSelectedAllocationTables()">
                            <i class="fa fa-download"></i> 导出选中
                        </a>
                        <a class="btn action-btn notify-btn" onclick="notifyAllocationTableCheck()" shiro:hasAnyRoles="admin,expense_admin">
                            <i class="fa fa-bell"></i> 通知核对
                        </a>
                        <a class="btn action-btn delete-btn" onclick="$.operate.removeAll()" shiro:hasAnyRoles="admin,expense_admin">
                            <i class="fa fa-trash"></i> 删除
                        </a>
                    </div>
                </div>
            </div>

            <!-- 表格区域 -->
            <div class="col-sm-12">
                <div class="table-section">
                    <!-- 表格标题 -->
                    <div class="table-header">
                        <h2><i class="fa fa-table"></i>分摊表管理</h2>
                    </div>
                    <table id="bootstrap-table"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认分摊表对话框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="confirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="confirmModalLabel"><i class="fa fa-check-circle"></i> 确认分摊表</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="confirmForm">
                        <input type="hidden" id="confirmId" name="id">
                        <div class="form-group">
                            <label for="responsiblePerson">负责人 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="responsiblePerson" name="responsiblePerson" placeholder="请输入负责人姓名" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                    <button type="button" class="btn btn-success" onclick="submitConfirm()">
                        <i class="fa fa-check"></i> 确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 退回分摊表对话框 -->
    <div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="rejectModalLabel"><i class="fa fa-times-circle"></i> 退回分摊表</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="rejectForm">
                        <input type="hidden" id="rejectId" name="id">
                        <div class="form-group">
                            <label for="rejectComments">退回原因 <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectComments" name="comments" rows="4" placeholder="请详细说明退回原因，以便制表人进行相应调整..." required></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                    <button type="button" class="btn btn-danger" onclick="submitReject()">
                        <i class="fa fa-reply"></i> 退回
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出分摊表对话框 -->
    <div class="modal fade" id="exportShareModal" tabindex="-1" role="dialog" aria-labelledby="exportShareModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="exportShareModalLabel"><i class="fa fa-download"></i> 导出分摊表</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white; opacity: 0.8;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="exportShareForm">
                        <div class="form-group">
                            <label for="exportExpenseType">费用类型 <span class="text-danger">*</span></label>
                            <select name="expenseType" id="exportExpenseType" class="form-control" required>
                                <option value="">请选择费用类型</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="exportBillingCycle">计费周期 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="exportBillingCycle" name="billingCycle" placeholder="请输入计费周期，如：202501 或 202501-06" required>
                            <small class="form-text text-muted">
                                <i class="fa fa-info-circle"></i> 格式：YYYYMM（如：202501）或 YYYYMM-MM（如：202501-06）
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fa fa-times"></i> 取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="submitExportShare()">
                        <i class="fa fa-download"></i> 导出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /*<![CDATA[*/
        var prefix = ctx + "expense/allocation_table";
        var isAdmin = /*[[${isAdmin}]]*/ false;
        var isChecker = /*[[${isChecker}]]*/ false;
        var currentUser = /*[[${currentUser}]]*/ '';
        /*]]>*/

        $(function() {
            var options = {
                url: prefix + "/list",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "分摊表管理",
                uniqueId: "id", // 设置唯一ID字段，用于局部刷新
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'tableName',
                    title: '分摊表名称',
                    sortable: true
                },
                {
                    field: 'billingCycle',
                    title: '计费周期',
                    sortable: true
                },
                {
                    field: 'expenseType',
                    title: '费用类型',
                    sortable: true
                },
                {
                    field: 'preparer',
                    title: '制表人'
                },
                {
                    field: 'reviewer',
                    title: '复核人'
                },
                {
                    field: 'responsiblePerson',
                    title: '负责人'
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value == '审核中') {
                            return '<span class="badge badge-warning">审核中</span>';
                        } else if (value == '已审核') {
                            return '<span class="badge badge-success">已审核</span>';
                        } else if (value == '已拒绝') {
                            return '<span class="badge badge-danger">已拒绝</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'comments',
                    title: '修改意见',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '';
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    field: 'confirmTime',
                    title: '确认时间',
                    sortable: true,
                    formatter: function(value, row, index) {
                        return value ? value : '未确认';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];

                        // 分摊表核对人可以确认和退回审核中的分摊表
                        if (isChecker && row.status == '审核中') {
                            actions.push('<a class="btn btn-success btn-xs" href="#" onclick="confirmAllocationTable(' + row.id + ')"><i class="fa fa-check"></i> 确认</a> ');
                            actions.push('<a class="btn btn-danger btn-xs" href="#" onclick="rejectAllocationTable(' + row.id + ')"><i class="fa fa-times"></i> 退回</a> ');
                        }

                        // 管理员和分摊表核对人可以在线查看和导出
                        if (isAdmin || isChecker) {
                            actions.push('<a class="btn btn-info btn-xs" href="#" onclick="viewAllocationTable(' + row.id + ')"><i class="fa fa-eye"></i> 在线查看</a> ');
                            actions.push('<a class="btn btn-primary btn-xs" href="#" onclick="exportAllocationTable(' + row.id + ')"><i class="fa fa-download"></i> 导出</a> ');
                        }

                        // 管理员可以对审核中的分摊表发送单独通知核对
                        if (isAdmin && row.status == '审核中') {
                            actions.push('<a class="btn btn-warning btn-xs" href="#" onclick="notifySingleAllocationTableCheck(' + row.id + ')"><i class="fa fa-bell"></i> 通知核对</a> ');
                        }

                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
            
            // 加载费用类型下拉选项
            loadExpenseTypes();
        });

        // 切换筛选表单的显示/隐藏
        function toggleSearchForm() {
            var $searchForm = $("#searchForm");
            var $toggleBtn = $("#searchToggleBtn");
            var $toggleIcon = $("#searchToggleIcon");

            if ($searchForm.is(":visible")) {
                // 当前显示，需要隐藏
                $searchForm.slideUp(400, function() {
                    $toggleBtn.html('<i class="fa fa-filter" style="margin-right: 8px;"></i><i class="fa fa-chevron-down" id="searchToggleIcon"></i> 高级筛选');
                });
            } else {
                // 当前隐藏，需要显示
                $searchForm.slideDown(400, function() {
                    $toggleBtn.html('<i class="fa fa-filter" style="margin-right: 8px;"></i><i class="fa fa-chevron-up" id="searchToggleIcon"></i> 收起筛选');
                });
            }
        }

        // 加载费用类型
        function loadExpenseTypes() {
            $.post(ctx + "expense/bill_manage/getDictData", { dictType: "expense_type" }, function(result) {
                if (result.code == 0 && result.data) {
                    var $select = $("#expenseType");
                    $select.empty().append('<option value="">请选择费用类型</option>');
                    $.each(result.data, function(i, item) {
                        $select.append('<option value="' + item.dictValue + '">' + item.dictLabel + '</option>');
                    });
                }
            });
        }

        // 确认分摊表
        function confirmAllocationTable(id) {
            // 直接确认，不弹出对话框
            $.post(prefix + "/confirm", {
                id: id
            }, function(result) {
                if (result.code == 0) {
                    $.modal.alertSuccess("分摊表确认成功");
                    // 局部刷新表格数据，只刷新当前行
                    refreshTableRow(id);
                } else {
                    $.modal.alertError(result.msg || "确认失败");
                }
            });
        }

        // 提交确认（保留函数以防其他地方调用）
        function submitConfirm() {
            // 已改为直接确认，此函数保留为空
        }

        // 退回分摊表
        function rejectAllocationTable(id) {
            $("#rejectId").val(id);
            $("#rejectComments").val('');
            $("#rejectModal").modal('show');
        }

        // 提交退回
        function submitReject() {
            var id = $("#rejectId").val();
            var comments = $("#rejectComments").val();
            
            if (!comments) {
                $.modal.alertWarning("请输入退回原因");
                return;
            }
            
            // 直接退回，不需要二次确认
            $.post(prefix + "/reject", {
                id: id,
                comments: comments
            }, function(result) {
                if (result.code == 0) {
                    $.modal.alertSuccess("分摊表退回成功");
                    // 局部刷新表格数据，只刷新当前行
                    refreshTableRow(id);
                    $("#rejectModal").modal('hide');
                } else {
                    $.modal.alertError(result.msg || "退回失败");
                }
            });
        }

        // 在线查看分摊表
        function viewAllocationTable(id) {
            var url = prefix + "/viewDetail/" + id;
            $.modal.open("在线查看分摊表", url, 1300, 700);
        }

        // 导出分摊表
        function exportAllocationTable(id) {
            // 直接导出，不需要二次确认
            window.location.href = prefix + "/exportDetail/" + id;
        }


        // 提交导出分摊表
        function submitExportShare() {
            var expenseType = $("#exportExpenseType").val();
            var billingCycle = $("#exportBillingCycle").val();
            
            if (!expenseType) {
                $.modal.alertWarning("请选择费用类型");
                return;
            }
            
            if (!billingCycle) {
                $.modal.alertWarning("请输入计费周期");
                return;
            }
            
            // 验证计费周期格式
            var singleMonthPattern = /^\d{6}$/;
            var rangeMonthPattern = /^\d{6}-\d{2}$/;
            if (!singleMonthPattern.test(billingCycle) && !rangeMonthPattern.test(billingCycle)) {
                $.modal.alertWarning("计费周期格式不正确，请输入YYYYMM格式（如：202501）或YYYYMM-MM格式（如：202501-06）");
                return;
            }
            
            $.modal.confirm("是否要导出该分摊表？", function() {
                // 构造导出URL
                var url = ctx + "expense/allocation_table/exportShareByParams?expenseType=" + encodeURIComponent(expenseType) + "&billingCycle=" + encodeURIComponent(billingCycle);
                
                // 在新窗口中打开导出链接
                window.open(url, "_blank");
                
                // 关闭弹窗
                $("#exportShareModal").modal('hide');
            });
        }

        // 加载导出分摊表的费用类型下拉选项
        function loadExportExpenseTypes() {
            $.post(ctx + "expense/bill_manage/getDictData", { dictType: "expense_type" }, function(result) {
                if (result.code == 0 && result.data) {
                    var $select = $("#exportExpenseType");
                    $select.empty().append('<option value="">请选择费用类型</option>');
                    $.each(result.data, function(i, item) {
                        $select.append('<option value="' + item.dictValue + '">' + item.dictLabel + '</option>');
                    });
                }
            });
        }

        // 局部刷新表格行数据
        function refreshTableRow(id) {
            // 获取当前表格实例
            var $table = $("#bootstrap-table");
            
            // 重新获取该行的最新数据
            $.post(prefix + "/getRowData", { id: id }, function(result) {
                if (result.code == 0 && result.data) {
                    // 使用Bootstrap Table的API更新指定行的数据
                    $table.bootstrapTable('updateByUniqueId', {
                        id: id,
                        row: result.data
                    });
                    
                    // 延迟一下再刷新，确保数据更新完成
                    setTimeout(function() {
                        // 重新渲染该行，确保操作按钮正确显示
                        $table.bootstrapTable('refreshRow', {
                            index: $table.bootstrapTable('getRowByUniqueId', id).index
                        });
                    }, 100);
                }
            });
        }

        // 导出选中的分摊表
        function exportSelectedAllocationTables() {
            var selectedIds = $.table.selectColumns('id');
            if (selectedIds.length == 0) {
                $.modal.alertWarning("请选择要导出的分摊表");
                return;
            }

            $.modal.confirm("确认要导出选中的 " + selectedIds.length + " 个分摊表吗？", function() {
                $.modal.loading("正在准备导出，请稍候...");

                // 使用延时机制逐个导出，避免浏览器弹窗阻止
                var exportIndex = 0;
                var exportInterval = setInterval(function() {
                    if (exportIndex < selectedIds.length) {
                        var id = selectedIds[exportIndex];
                        // 使用现有的exportAllocationTable逻辑，在新窗口中打开
                        window.open(prefix + "/exportDetail/" + id, "_blank");
                        exportIndex++;
                    } else {
                        // 所有导出完成
                        clearInterval(exportInterval);
                        $.modal.closeLoading();
                        $.modal.alertSuccess("已完成 " + selectedIds.length + " 个分摊表的导出，请检查浏览器下载");
                    }
                }, 500); // 每500毫秒导出一个，避免浏览器阻止
            });
        }

        // 一键导出所有审核中的分摊表
        function exportAllPendingAllocationTables() {
            $.modal.confirm("是否要导出所有处于'审核中'状态的分摊表吗？", function() {
                $.modal.loading("正在获取审核中的分摊表，请稍候...");

                // 先获取所有审核中状态的分摊表ID
                $.post(prefix + "/getPendingIds", function(result) {
                    if (result.code == 0 && result.data && result.data.length > 0) {
                        var pendingIds = result.data;
                        $.modal.loading("正在准备导出 " + pendingIds.length + " 个分摊表，请稍候...");

                        // 使用延时机制逐个导出，避免浏览器弹窗阻止
                        var exportIndex = 0;
                        var exportInterval = setInterval(function() {
                            if (exportIndex < pendingIds.length) {
                                var id = pendingIds[exportIndex];
                                // 使用现有的exportAllocationTable逻辑，在新窗口中打开
                                window.open(prefix + "/exportDetail/" + id, "_blank");
                                exportIndex++;
                            } else {
                                // 所有导出完成
                                clearInterval(exportInterval);
                                $.modal.closeLoading();
                                $.modal.alertSuccess("已完成 " + pendingIds.length + " 个审核中分摊表的导出，请检查浏览器下载");
                            }
                        }, 500); // 每500毫秒导出一个，避免浏览器阻止
                    } else {
                        $.modal.closeLoading();
                        $.modal.alertWarning("没有找到处于'审核中'状态的分摊表");
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("网络错误，请稍后重试");
                });
            });
        }

        // 一键确认所有审核中的分摊表
        function confirmAllPending() {
            $.modal.confirm("是否要一键确认所有处于'审核中'状态的分摊表？", function() {
                $.modal.loading("正在处理，请稍候...");

                $.post(prefix + "/confirmAllPending", function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("成功确认 " + result.data + " 个分摊表");
                        // 刷新整个表格
                        $("#bootstrap-table").bootstrapTable('refresh');
                    } else {
                        $.modal.alertError(result.msg || "一键确认失败");
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("网络错误，请稍后重试");
                });
            });
        }

        // 通知核对（通知所有审核中状态的分摊表）
        function notifyAllocationTableCheck() {
            $.modal.confirm("确定要通知所有处于'审核中'状态的分摊表进行核对吗？", function() {
                $.modal.loading("正在发送通知，请稍候...");

                $.post(prefix + "/notifyAllocationTableCheck", function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("通知发送成功：" + result.msg);
                    } else {
                        $.modal.alertError("通知发送失败：" + (result.msg || "未知错误"));
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("网络错误，请稍后重试");
                });
            });
        }

        // 单独通知核对（通知指定的分摊表）
        function notifySingleAllocationTableCheck(allocationTableId) {
            $.modal.confirm("确定要通知该分摊表进行核对吗？", function() {
                $.modal.loading("正在发送通知，请稍候...");

                $.post(prefix + "/notifySingleAllocationTableCheck", {
                    allocationTableId: allocationTableId
                }, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("通知发送成功：" + result.msg);
                    } else {
                        $.modal.alertError("通知发送失败：" + (result.msg || "未知错误"));
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("网络错误，请稍后重试");
                });
            });
        }
    </script>
</body>
</html> 