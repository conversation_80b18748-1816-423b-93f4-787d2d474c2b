<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格样式预览</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .container-div {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .table-section {
            background: white;
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border: 2px solid #e3f2fd;
            overflow: hidden;
        }

        .table-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            margin-bottom: 0;
            box-shadow: 0 2px 10px rgba(116, 185, 255, 0.2);
            border-bottom: 3px solid #1976d2;
        }

        .table-header h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .table-header h2 i {
            margin-right: 12px;
            font-size: 18px;
        }
        
        .table {
            border: none;
            border-radius: 0;
            overflow: hidden;
            box-shadow: none;
        }
        
        .table thead th {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            font-weight: 700;
            border: 2px solid #e3f2fd;
            border-bottom: 3px solid #1976d2;
            padding: 18px 15px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .table thead th:first-child {
            border-radius: 0;
            border-left: 3px solid #1976d2;
        }

        .table thead th:last-child {
            border-radius: 0;
            border-right: 3px solid #1976d2;
        }
        
        .table thead th:before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #00b894, #74b9ff, #0984e3);
            opacity: 0.8;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #e3f2fd;
        }
        
        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.15);
        }
        
        .table tbody tr:last-child {
            border-bottom: none;
        }
        
        .table tbody td {
            padding: 15px 12px;
            vertical-align: middle;
            border-left: 1px solid #e3f2fd;
            border-right: 1px solid #e3f2fd;
            position: relative;
        }
        
        .table tbody td:first-child {
            border-left: 2px solid #74b9ff;
        }
        
        .table tbody td:last-child {
            border-right: 2px solid #74b9ff;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #fafbfc;
        }
        
        .table tbody tr:nth-child(odd) {
            background-color: white;
        }
        
        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-warning {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }
        
        .badge-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
        }
        
        .badge-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
        }
        
        .btn-xs {
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 15px;
            margin: 2px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-xs:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container-div">
        <div class="table-section">
            <div class="table-header">
                <h2><i class="fa fa-table"></i>分摊表管理</h2>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th><input type="checkbox"></th>
                        <th>分摊表名称</th>
                        <th>计费周期</th>
                        <th>费用类型</th>
                        <th>制表人</th>
                        <th>复核人</th>
                        <th>负责人</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2025年1月电费分摊表</td>
                        <td>202501</td>
                        <td>电费</td>
                        <td>张三</td>
                        <td>李四</td>
                        <td>王五</td>
                        <td><span class="badge badge-warning">审核中</span></td>
                        <td>2025-01-15 10:30:00</td>
                        <td>
                            <a class="btn btn-success btn-xs"><i class="fa fa-check"></i> 确认</a>
                            <a class="btn btn-danger btn-xs"><i class="fa fa-times"></i> 退回</a>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2024年12月水费分摊表</td>
                        <td>202412</td>
                        <td>水费</td>
                        <td>赵六</td>
                        <td>钱七</td>
                        <td>孙八</td>
                        <td><span class="badge badge-success">已审核</span></td>
                        <td>2024-12-28 14:20:00</td>
                        <td>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                            <a class="btn btn-primary btn-xs"><i class="fa fa-download"></i> 导出</a>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2024年11月物业费分摊表</td>
                        <td>202411</td>
                        <td>物业费</td>
                        <td>周九</td>
                        <td>吴十</td>
                        <td>郑十一</td>
                        <td><span class="badge badge-danger">已拒绝</span></td>
                        <td>2024-11-25 16:45:00</td>
                        <td>
                            <a class="btn btn-warning btn-xs"><i class="fa fa-edit"></i> 修改</a>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2024年10月网络费分摊表</td>
                        <td>202410</td>
                        <td>网络费</td>
                        <td>王十二</td>
                        <td>李十三</td>
                        <td>张十四</td>
                        <td><span class="badge badge-success">已审核</span></td>
                        <td>2024-10-30 09:15:00</td>
                        <td>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                            <a class="btn btn-primary btn-xs"><i class="fa fa-download"></i> 导出</a>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td>2024年9月清洁费分摊表</td>
                        <td>202409</td>
                        <td>清洁费</td>
                        <td>赵十五</td>
                        <td>钱十六</td>
                        <td>孙十七</td>
                        <td><span class="badge badge-warning">审核中</span></td>
                        <td>2024-09-28 13:20:00</td>
                        <td>
                            <a class="btn btn-success btn-xs"><i class="fa fa-check"></i> 确认</a>
                            <a class="btn btn-danger btn-xs"><i class="fa fa-times"></i> 退回</a>
                            <a class="btn btn-info btn-xs"><i class="fa fa-eye"></i> 查看</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
